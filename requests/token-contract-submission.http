### Token Contract Submission Endpoint Tests

@baseUrl = https://api.testnet.caishen.io
@projectId = your-project-id-here

### Submit token contract address for approved project (protected endpoint)
POST {{baseUrl}}/projects/{{projectId}}/submit-token-contract
Content-Type: application/json
<PERSON>ie: {{sessionCookie}}

{
  "tokenContractAddress": "******************************************"
}

### Submit token contract address for approved project - Solana address
POST {{baseUrl}}/projects/{{projectId}}/submit-token-contract  
Content-Type: application/json
<PERSON>ie: {{sessionCookie}}

{
  "tokenContractAddress": "DRiP2Pn2K6fuMLKQmt5rZWxa91wMobTEuQ7sPTtfGMWE"
}

### Test with invalid token contract address (should fail validation)
POST {{baseUrl}}/projects/{{projectId}}/submit-token-contract
Content-Type: application/json
<PERSON>ie: {{sessionCookie}}

{
  "tokenContractAddress": "invalid-contract-address"
}

### Test with non-existent project (should return 404)
POST {{baseUrl}}/projects/non-existent-project-id/submit-token-contract
Content-Type: application/json
Cookie: {{sessionCookie}}

{
  "tokenContractAddress": "******************************************"
}

### Get project details to verify status change
GET {{baseUrl}}/projects/{{projectId}}
Cookie: {{sessionCookie}}

### Submit wallet address after token contract (should work after token_deployed status)
POST {{baseUrl}}/projects/{{projectId}}/submit-wallet
Content-Type: application/json
Cookie: {{sessionCookie}}

{
  "walletAddress": "******************************************"
} 