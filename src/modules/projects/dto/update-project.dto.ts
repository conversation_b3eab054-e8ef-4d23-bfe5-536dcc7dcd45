import { ApiProperty } from '@nestjs/swagger'
import { createZodDto } from 'nestjs-zod'
import { z } from 'zod'

import { blockchainEnum, tgeTypeEnum, launchTypeEnum } from '../../../libs/database/schema'

// Create a comprehensive schema for updating project fields
const updateProjectSchema = z.object({
  // Basic Information
  projectName: z.string().min(1).max(255).optional(),
  projectWebsite: z.string().url().max(2048).optional(),
  contactName: z.string().min(1).max(255).optional(),
  contactEmail: z.string().email().max(255).optional(),
  roleInProject: z.string().min(1).max(255).optional(),
  blockchainToLaunchOn: z.enum(blockchainEnum.enumValues as [string, ...string[]]).optional(),

  // Launch Type and Curation
  launchType: z.enum(launchTypeEnum.enumValues as [string, ...string[]]).optional(),
  isCurated: z.boolean().optional(),
  partnerName: z.string().max(255).nullable().optional(),

  // Project Details (JSON fields)
  projectDetails: z.any().optional(), // Editor.js JSON
  teamIntroduction: z.any().optional(), // Editor.js JSON
  teamPublicProfileLinks: z.string().optional(),

  // Fundraising
  fundraiseHistory: z.any().optional(), // Editor.js JSON
  totalRaisedUsd: z
    .string()
    .regex(/^\d+(\.\d{1,2})?$/)
    .optional(),

  // Community & Social
  officialTwitter: z.string().url().max(2048).optional(),
  discordUrl: z.string().url().max(2048).nullable().optional(),
  telegram: z.string().max(2048).nullable().optional(),
  farcaster: z.string().max(2048).nullable().optional(),
  communitySizeEngagement: z.any().optional(), // Editor.js JSON

  // Tokenomics
  tokenomicsDetails: z.any().optional(), // Editor.js JSON

  // Security
  securityAuditsGeneralInfo: z.any().optional(), // Editor.js JSON

  // Token Information
  tokenName: z.string().min(1).max(255).optional(),
  tokenSymbol: z.string().min(1).max(50).optional(),
  tokenContractAddress: z.string().min(1).max(255).optional().nullable(),
  totalTokenSupply: z.string().regex(/^\d+$/).optional(),
  tokenLaunchMarketCapUsd: z
    .string()
    .regex(/^\d+(\.\d{1,2})?$/)
    .optional(),
  tokenThumbnailImageUrl: z.string().url().max(2048).nullable().optional(),

  // TGE Details
  tgeType: z.enum(tgeTypeEnum.enumValues as [string, ...string[]]).optional(),
  allocationTotalSupplyCaishen: z
    .string()
    .regex(/^\d+(\.\d{1,2})?$/)
    .optional(),
  allocationCirculatingSupplyCaishen: z
    .string()
    .regex(/^\d+(\.\d{1,2})?$/)
    .optional(),
  acceptedCurrenciesForPairing: z.string().min(1).max(255).optional(),
  tgePriceUsdc: z
    .string()
    .regex(/^\d+(\.\d{1,9})?$/)
    .optional(),
  exclusiveTradingPeriodHours: z.enum(['24', '36', '48', '60', '72']).transform(Number).optional(),
  expectedTgeLaunchDate: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/)
    .optional(),
  liquidityActivationDate: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/)
    .optional(),
  minAmountPerTrade: z
    .string()
    .regex(/^\d+(\.\d{1,9})?$/)
    .optional(),
  // maxAmountPerTradeSol: removed - no longer needed

  // Marketing
  preLaunchMarketingStrategy: z.any().optional(), // Editor.js JSON

  // Banner & Header
  headerDescription: z.any().optional(), // Editor.js JSON
  bannerBackgroundImageUrl: z.string().url().max(2048).nullable().optional(),

  // Post-Approval Fields
  providedWalletAddress: z.string().max(255).nullable().optional(),
})

export class UpdateProjectDto extends createZodDto(updateProjectSchema) {
  @ApiProperty({
    example: 'DeFi Revolution Protocol',
    description: 'Project name',
    required: false,
  })
  projectName?: string

  @ApiProperty({
    example: 'https://defirevolution.com',
    description: 'Project website URL',
    required: false,
  })
  projectWebsite?: string

  @ApiProperty({
    example: 'John Doe',
    description: 'Contact person name',
    required: false,
  })
  contactName?: string

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Contact email address',
    required: false,
  })
  contactEmail?: string

  @ApiProperty({
    example: 'Founder & CEO',
    description: 'Role in the project',
    required: false,
  })
  roleInProject?: string

  @ApiProperty({
    example: 'ethereum',
    enum: blockchainEnum.enumValues,
    description: 'Blockchain to launch on',
    required: false,
  })
  blockchainToLaunchOn?: string

  @ApiProperty({
    example: 'fair_launch',
    enum: launchTypeEnum.enumValues,
    description: 'Launch type',
    required: false,
  })
  launchType?: string

  @ApiProperty({
    example: false,
    description: 'Whether the project is curated',
    required: false,
  })
  isCurated?: boolean

  @ApiProperty({
    example: 'Partner Firm',
    description: 'Partner name for curated projects',
    required: false,
  })
  partnerName?: string | null

  @ApiProperty({
    example: 'DeFi Token',
    description: 'Token name',
    required: false,
  })
  tokenName?: string

  @ApiProperty({
    example: 'DFT',
    description: 'Token symbol',
    required: false,
  })
  tokenSymbol?: string

  @ApiProperty({
    example: '******************************************',
    description: 'Token contract address',
    required: false,
  })
  tokenContractAddress?: string

  @ApiProperty({
    example: '1000000000',
    description: 'Total token supply',
    required: false,
  })
  totalTokenSupply?: string

  @ApiProperty({
    example: '10000000.00',
    description: 'Token launch market cap in USD',
    required: false,
  })
  tokenLaunchMarketCapUsd?: string

  @ApiProperty({
    example: 'https://twitter.com/defirevolution',
    description: 'Official Twitter URL',
    required: false,
  })
  officialTwitter?: string

  @ApiProperty({
    example: '1000000.00',
    description: 'Total amount raised in USD',
    required: false,
  })
  totalRaisedUsd?: string

  // Add other properties as needed for the API documentation
}
