import { ApiProperty } from '@nestjs/swagger'
import { createZodDto } from 'nestjs-zod'
import { z } from 'zod'

const submitTokenContractSchema = z.object({
  tokenContractAddress: z
    .string()
    .min(1, 'Token contract address is required')
    .max(255)
    .refine(
      (address) => {
        // EVM addresses (Ethereum, Polygon, BSC, Arbitrum, Base, Optimism)
        const evmRegex = /^(0x)[0-9A-Fa-f]{40}$/
        // Solana addresses
        const solanaRegex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/

        return evmRegex.test(address) || solanaRegex.test(address)
      },
      {
        message: 'Invalid token contract address format. Must be a valid EVM address (0x...) or Solana address',
      },
    ),
})

export class SubmitTokenContractDto extends createZodDto(submitTokenContractSchema) {
  @ApiProperty({
    example: '******************************************',
    description: 'Token contract address after deployment. Must be a valid EVM address (0x...) or Solana address',
    pattern: '^(0x)[0-9A-Fa-f]{40}$|^[1-9A-HJ-NP-Za-km-z]{32,44}$',
  })
  tokenContractAddress!: string
}
