import { Injectable, Inject } from '@nestjs/common'
import { sql, eq, and, or, desc, inArray, type InferSelectModel } from 'drizzle-orm'
import { NodePgDatabase } from 'drizzle-orm/node-postgres'

import { projects, projectCategories, projectHasCategories, projectAudits } from '../../../libs/database/schema'
import * as schema from '../../../libs/database/schema'

// Type definitions
type ProjectSelect = InferSelectModel<typeof projects>

interface GetApprovedProjectsParams {
  category?: 'upcoming' | 'exclusive' | 'live'
  blockchain?: string
  search?: string
  page: number
  limit: number
  isCurated?: boolean
}

@Injectable()
export class PublicProjectsService {
  constructor(@Inject('DB') private db: NodePgDatabase<typeof schema>) {}

  async getApprovedProjects(params: GetApprovedProjectsParams) {
    const { category, blockchain, search, page, limit, isCurated } = params
    const offset = (page - 1) * limit

    // Build where conditions
    const whereConditions = [eq(projects.submissionStatus, 'liquidity_provided')]

    // Blockchain filter
    if (blockchain) {
      whereConditions.push(eq(projects.blockchainToLaunchOn, blockchain as ProjectSelect['blockchainToLaunchOn']))
    }

    // isCurated filter
    if (isCurated !== undefined) {
      whereConditions.push(eq(projects.isCurated, isCurated))
    }

    // Search filter (project name or token symbol)
    if (search) {
      whereConditions.push(
        or(
          sql`LOWER(${projects.projectName}) LIKE ${`%${search.toLowerCase()}%`}`,
          sql`LOWER(${projects.tokenSymbol}) LIKE ${`%${search.toLowerCase()}%`}`,
        )!,
      )
    }

    // Category filter based on TGE date and exclusive trading period
    if (category) {
      const now = new Date()

      switch (category) {
        case 'upcoming':
          // today < expectedTgeLaunchDate
          whereConditions.push(sql`${projects.expectedTgeLaunchDate} > ${now}`)
          break

        case 'exclusive':
          // expectedTgeLaunchDate <= today < expectedTgeLaunchDate + exclusivePeriod
          whereConditions.push(
            sql`${projects.expectedTgeLaunchDate} <= ${now} AND ${now} < (${projects.expectedTgeLaunchDate} + INTERVAL '1 hour' * ${projects.exclusiveTradingPeriodHours})`,
          )
          break

        case 'live':
          // today >= expectedTgeLaunchDate + exclusivePeriod
          whereConditions.push(
            sql`${now} >= (${projects.expectedTgeLaunchDate} + INTERVAL '1 hour' * ${projects.exclusiveTradingPeriodHours})`,
          )
          break

        default:
          // No additional filtering for unknown categories
          break
      }
    }

    // Get total count for pagination
    const [{ count: total }] = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(projects)
      .where(and(...whereConditions))

    // Get projects with categories
    const projectsList = await this.db
      .select({
        projectId: projects.projectId,
        projectName: projects.projectName,
        tokenSymbol: projects.tokenSymbol,
        blockchainToLaunchOn: projects.blockchainToLaunchOn,
        startDate: projects.expectedTgeLaunchDate,
        exclusiveTradingPeriodHours: projects.exclusiveTradingPeriodHours,
        status: projects.submissionStatus,
        initialPrice: projects.tgePriceUsdc,
        tokenThumbnailImageUrl: projects.tokenThumbnailImageUrl,
        headerDescription: projects.headerDescription,
        bannerBackgroundImageUrl: projects.bannerBackgroundImageUrl,
        projectWebsite: projects.projectWebsite,
        officialTwitter: projects.officialTwitter,
        discordUrl: projects.discordUrl,
        telegram: projects.telegram,
        farcaster: projects.farcaster,
        tokenId: projects.tokenId,
        // New curation fields
        launchType: projects.launchType,
        isCurated: projects.isCurated,
        partnerName: projects.partnerName,
      })
      .from(projects)
      .where(and(...whereConditions))
      .orderBy(desc(projects.expectedTgeLaunchDate))
      .limit(limit)
      .offset(offset)

    // Get categories for each project
    const projectIds = projectsList.map((p) => p.projectId)
    const categoriesData = projectIds.length
      ? await this.db
          .select({
            projectId: projectHasCategories.projectId,
            categoryId: projectCategories.categoryId,
            categoryName: projectCategories.categoryName,
          })
          .from(projectHasCategories)
          .innerJoin(projectCategories, eq(projectHasCategories.categoryId, projectCategories.categoryId))
          .where(inArray(projectHasCategories.projectId, projectIds))
      : []

    // Group categories by project
    const categoriesByProject = categoriesData.reduce(
      (acc, cat) => {
        if (!acc[cat.projectId]) acc[cat.projectId] = []

        acc[cat.projectId].push({
          categoryId: cat.categoryId,
          categoryName: cat.categoryName,
        })

        return acc
      },
      {} as Record<string, { categoryId: number; categoryName: string }[]>,
    )

    // Format response
    const formattedProjects = projectsList.map((project) => {
      // Calculate endDate using the same logic as single project endpoint
      const { startDate, exclusiveTradingPeriodHours } = project
      const exclusiveTradingHours = exclusiveTradingPeriodHours || 24
      const endDate = new Date(new Date(startDate).getTime() + exclusiveTradingHours * 60 * 60 * 1000)

      return {
        projectId: project.projectId,
        projectName: project.projectName,
        tokenSymbol: project.tokenSymbol,
        blockchainToLaunchOn: project.blockchainToLaunchOn,
        startDate: new Date(startDate).toISOString(),
        endDate: endDate.toISOString(),
        status: project.status,
        initialPrice: project.initialPrice,
        tokenThumbnailImageUrl: project.tokenThumbnailImageUrl,
        headerDescription: project.headerDescription,
        bannerBackgroundImageUrl: project.bannerBackgroundImageUrl,
        tokenId: project.tokenId,
        // New curation fields
        launchType: project.launchType,
        isCurated: project.isCurated,
        partnerName: project.partnerName,
        categories: categoriesByProject[project.projectId] || [],
        socialLinks: {
          website: project.projectWebsite,
          twitter: project.officialTwitter,
          discord: project.discordUrl,
          telegram: project.telegram,
          farcaster: project.farcaster,
        },
      }
    })

    return {
      projects: formattedProjects,
      pagination: {
        page,
        limit,
        total,
        hasMore: offset + limit < total,
      },
    }
  }

  async getApprovedProjectById(projectId: string) {
    // Get project details - only if liquidity is provided (live projects)
    const projectResult = await this.db
      .select()
      .from(projects)
      .where(and(eq(projects.projectId, projectId), eq(projects.submissionStatus, 'liquidity_provided')))
      .limit(1)

    if (!projectResult.length) {
      return null
    }

    const project = projectResult[0]

    // Get project categories
    const categories = await this.db
      .select({
        categoryId: projectCategories.categoryId,
        categoryName: projectCategories.categoryName,
      })
      .from(projectHasCategories)
      .innerJoin(projectCategories, eq(projectHasCategories.categoryId, projectCategories.categoryId))
      .where(eq(projectHasCategories.projectId, projectId))

    // Get project audits
    const audits = await this.db
      .select({
        auditId: projectAudits.auditId,
        auditDate: projectAudits.auditDate,
        auditingFirm: projectAudits.auditingFirm,
        auditReportUrl: projectAudits.auditReportUrl,
        auditingFirmLogoUrl: projectAudits.auditingFirmLogoUrl,
      })
      .from(projectAudits)
      .where(eq(projectAudits.projectId, projectId))

    // Calculate startDate and endDate (same logic as in list endpoint)
    const startDate = project.expectedTgeLaunchDate
    const exclusiveTradingHours = project.exclusiveTradingPeriodHours || 24
    const endDate = new Date(new Date(startDate).getTime() + exclusiveTradingHours * 60 * 60 * 1000)

    // Format and return complete project details
    return {
      projectId: project.projectId,
      projectName: project.projectName,
      projectWebsite: project.projectWebsite,
      blockchainToLaunchOn: project.blockchainToLaunchOn,

      // Add startDate, endDate, and poolAddress at top level (same as list endpoint)
      startDate: new Date(startDate).toISOString(),
      endDate: endDate.toISOString(),
      poolAddress: project.poolAddress,

      // Curation fields
      launchType: project.launchType,
      isCurated: project.isCurated,
      partnerName: project.partnerName,

      // Rich text content
      projectDetails: project.projectDetails,
      teamIntroduction: project.teamIntroduction,
      fundraiseHistory: project.fundraiseHistory,
      totalRaisedUsd: project.totalRaisedUsd,
      tokenomicsDetails: project.tokenomicsDetails,
      preLaunchMarketingStrategy: project.preLaunchMarketingStrategy,
      headerDescription: project.headerDescription,

      // Social links
      socialLinks: {
        website: project.projectWebsite,
        twitter: project.officialTwitter,
        discord: project.discordUrl,
        telegram: project.telegram,
        farcaster: project.farcaster,
      },

      // Token information
      tokenInfo: {
        tokenName: project.tokenName,
        tokenSymbol: project.tokenSymbol,
        tokenContractAddress: project.tokenContractAddress,
        poolAddress: project.poolAddress,
        totalTokenSupply: project.totalTokenSupply,
        tokenLaunchMarketCapUsd: project.tokenLaunchMarketCapUsd,
        tokenThumbnailImageUrl: project.tokenThumbnailImageUrl,
      },

      // TGE details
      tgeDetails: {
        tgeType: project.tgeType,
        allocationTotalSupplyCaishen: project.allocationTotalSupplyCaishen,
        allocationCirculatingSupplyCaishen: project.allocationCirculatingSupplyCaishen,
        acceptedCurrenciesForPairing: project.acceptedCurrenciesForPairing,
        tgePriceUsdc: project.tgePriceUsdc,
        exclusiveTradingPeriodHours: project.exclusiveTradingPeriodHours,
        expectedTgeLaunchDate: project.expectedTgeLaunchDate,
        liquidityActivationDate: project.liquidityActivationDate,
        minAmountPerTrade: project.minAmountPerTrade,
      },

      // Related data
      categories,
      audits,

      // Metadata
      submissionStatus: project.submissionStatus,
      tokenId: project.tokenId,
      createdAt: project.createdAt,
      updatedAt: project.updatedAt,
    }
  }
}
