import { Injectable, BadRequestException } from '@nestjs/common'

@Injectable()
export class ProjectValidationService {
  // Define required fields for each step
  private readonly STEP_REQUIRED_FIELDS: Record<number, string[]> = {
    1: ['projectName', 'projectWebsite', 'contactName', 'contactEmail', 'roleInProject', 'blockchainToLaunchOn'],
    2: ['projectDetails'],
    3: ['teamIntroduction', 'teamPublicProfileLinks'],
    4: ['fundraiseHistory', 'totalRaisedUsd'],
    5: ['officialTwitter'],
    6: ['tokenomicsDetails'],
    7: ['securityAuditsGeneralInfo'],
    8: ['tokenName', 'tokenSymbol', 'totalTokenSupply', 'tokenLaunchMarketCapUsd'],
    9: [
      'tgeType',
      'allocationTotalSupplyCaishen',
      'allocationCirculatingSupplyCaishen',
      'acceptedCurrenciesForPairing',
      'tgePriceUsdc',
      'exclusiveTradingPeriodHours',
      'expectedTgeLaunchDate',
      'liquidityActivationDate',
      'minAmountPerTrade',
    ],
    10: ['preLaunchMarketingStrategy'],
    11: ['headerDescription'],
  }

  validateStep(step: number, projectData: any): { isValid: boolean; missingFields: string[] } {
    const requiredFields = this.STEP_REQUIRED_FIELDS[step]

    if (!requiredFields) {
      throw new BadRequestException(`Invalid step: ${step}`)
    }

    const missingFields: string[] = []

    for (const field of requiredFields) {
      if (!projectData[field] || projectData[field] === '' || projectData[field] === null) {
        missingFields.push(field)
      }
    }

    // Special validation for Step 9 - TGE price validation
    if (step === 9 && projectData.tgePriceUsdc) {
      const tgePriceValidation = this.validateTgePrice(projectData.tgePriceUsdc)

      if (!tgePriceValidation.isValid) {
        missingFields.push(`tgePriceUsdc (${tgePriceValidation.error})`)
      }
    }

    return {
      isValid: missingFields.length === 0,
      missingFields,
    }
  }

  validateForSubmission(projectData: any): { isValid: boolean; missingFields: string[] } {
    const allMissingFields: string[] = []

    // Check all steps
    for (let step = 1; step <= 11; step += 1) {
      const { missingFields } = this.validateStep(step, projectData)
      allMissingFields.push(...missingFields)
    }

    return {
      isValid: allMissingFields.length === 0,
      missingFields: allMissingFields,
    }
  }

  validateEmailFormat(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

    return emailRegex.test(email)
  }

  validateUrlFormat(url: string): boolean {
    try {
      void new URL(url)

      return true
    } catch {
      return false
    }
  }

  validateTokenSymbol(symbol: string): boolean {
    // Token symbol should be 2-10 characters, alphanumeric
    const symbolRegex = /^[A-Z0-9]{2,10}$/

    return symbolRegex.test(symbol.toUpperCase())
  }

  validateContractAddress(address: string, blockchain: string): boolean {
    switch (blockchain) {
      case 'ethereum':
      case 'polygon':
      case 'bsc':
      case 'arbitrum':
      case 'base':
      case 'optimism':
        // Ethereum-style address validation
        return /^0x[a-fA-F0-9]{40}$/.test(address)
      case 'solana':
        // Solana address validation (base58, 32-44 characters)
        return /^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address)
      default:
        return false
    }
  }

  validateWalletAddress(address: string): { isValid: boolean; addressType?: 'evm' | 'solana' } {
    // EVM addresses (Ethereum, Polygon, BSC, Arbitrum, Base, Optimism)
    const evmRegex = /^(0x)[0-9A-Fa-f]{40}$/
    // Solana addresses
    const solanaRegex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/

    if (evmRegex.test(address)) {
      return { isValid: true, addressType: 'evm' }
    }

    if (solanaRegex.test(address)) {
      return { isValid: true, addressType: 'solana' }
    }

    return { isValid: false }
  }

  validateTgePrice(price: string): { isValid: boolean; error?: string } {
    if (!price || price.trim() === '') {
      return { isValid: false, error: 'TGE price is required' }
    }

    const numPrice = parseFloat(price)

    if (Number.isNaN(numPrice)) {
      return { isValid: false, error: 'TGE price must be a valid number' }
    }

    if (numPrice <= 0) {
      return { isValid: false, error: 'TGE price must be greater than 0' }
    }

    if (numPrice > 1000000) {
      return { isValid: false, error: 'TGE price is unreasonably high (max: 1,000,000 USDC)' }
    }

    return { isValid: true }
  }
}
