import { Injectable, Logger, Inject } from '@nestjs/common'
import BigNumber from 'bignumber.js'
import { eq, type InferSelectModel } from 'drizzle-orm'
import { NodePgDatabase } from 'drizzle-orm/node-postgres'

import { PoolIndexerService } from '@/libs/indexer'
import { getErrorMessage } from '@/utils/get-error-message'

import * as schema from '../../../libs/database/schema'
import { projects, projectCategories, projectHasCategories } from '../../../libs/database/schema'
import { ChainsService } from '../../chains'
import { TokensService, type TokenInfo, type TokenToCreate } from '../../tokens'
import { type TokenChainInfo } from '../../tokens/schemas/token-chain-info.schema'

// Proper type for project from schema
type ProjectData = InferSelectModel<typeof projects>

@Injectable()
export class LaunchpadTokenService {
  private readonly logger = new Logger(LaunchpadTokenService.name)

  constructor(
    @Inject('DB') private db: NodePgDatabase<typeof schema>,
    private tokensService: TokensService,
    private chainsService: ChainsService,
    private poolIndexerService: PoolIndexerService,
  ) {}

  /**
   * Create a launchpad token from project data
   */
  async createLaunchpadTokenFromProject(projectId: string): Promise<TokenInfo | null> {
    try {
      // Get project data
      const projectResult = await this.db.select().from(projects).where(eq(projects.projectId, projectId)).limit(1)

      if (!projectResult.length) {
        this.logger.error(`Project not found: ${projectId}`)

        return null
      }

      const project = projectResult[0]

      // Check if token already exists
      if (project.tokenId) {
        this.logger.warn(`Token already exists for project ${projectId}: ${project.tokenId}`)
        const existingToken = await this.tokensService.getTokenById(project.tokenId)

        return existingToken || null
      }

      // Generate chain info using ChainsService
      const chainInfo = await this.generateChainInfo(project)

      // Get project categories
      const categories = await this.getProjectCategories(projectId)

      // Map project data to TokenInfo
      const tokenToCreate: TokenToCreate = {
        tokenInfo: {
          ticker: project.tokenSymbol,
          fullName: project.tokenName,
          imageUrl: project.tokenThumbnailImageUrl || '',
          chains: chainInfo,
          cmcId: '', // Empty for launchpad tokens since they don't have CMC listings
          isStable: false,
          isViewOnly: false,
          isDefault: false,
          isTradable: false, // Launchpad tokens start as non-tradable
          isLaunchpadToken: true, // Mark as launchpad token
          coingeckoId: undefined,
          marketData: {
            description: project.headerDescription?.toString() || '', // Use headerDescription directly as it's plain text
            categories, // Use actual project categories
            tokenRank: 999999, // Default low rank for new tokens
            fdv: this.calculateFDV(project),
            totalSupply: this.parseTokenAmount(project.totalTokenSupply),
            circulatingSupply: this.calculateCirculatingSupply(project),
            liquidityDepth: 'poor',
            tradingVolume24h: 0,
            securityRating: 'no-info',
            links: {
              website: project.projectWebsite || undefined,
              twitter: project.officialTwitter || undefined,
              telergam: project.telegram || undefined,
              discord: project.discordUrl || undefined,
              warpcast: project.farcaster || undefined,
              instagram: undefined,
            },
          },
          displayInfo: {
            ticker: project.tokenSymbol,
            isChartHidden: true, // Make chart hidden as requested
            isAvailableForOnramp: false,
          },
        },
        rate: {
          rate: project.tgePriceUsdc.toString(),
          percentChange: '0',
          rateHistory: [],
        },
      }

      // Create token in Firebase
      const createdTokens = await this.tokensService.addTokens([tokenToCreate])

      if (!createdTokens.length) {
        throw new Error('Failed to create token in Firebase')
      }

      const createdToken = createdTokens[0]

      // Update project with tokenId
      await this.db
        .update(projects)
        .set({
          tokenId: createdToken.id,
          updatedAt: new Date(),
        })
        .where(eq(projects.projectId, projectId))

      this.logger.log(`Successfully created launchpad token ${createdToken.id} for project ${projectId}`)

      return createdToken
    } catch (error) {
      this.logger.error(`Failed to create launchpad token for project ${projectId}: ${getErrorMessage(error)}`)

      return null
    }
  }

  private async getProjectCategories(projectId: string): Promise<string[]> {
    try {
      const categories = await this.db
        .select({
          categoryName: projectCategories.categoryName,
        })
        .from(projectHasCategories)
        .innerJoin(projectCategories, eq(projectHasCategories.categoryId, projectCategories.categoryId))
        .where(eq(projectHasCategories.projectId, projectId))

      return categories.map((cat) => cat.categoryName)
    } catch (error) {
      this.logger.error(`Failed to get categories for project ${projectId}:`, error)

      return []
    }
  }

  private async generateChainInfo(project: ProjectData): Promise<TokenChainInfo[]> {
    // Get chain info from ChainsService
    const chainData = await this.chainsService.getChainById(project.blockchainToLaunchOn)

    if (!chainData) {
      throw new Error(`Chain not found: ${project.blockchainToLaunchOn}`)
    }

    // Validate token contract address exists
    if (!project.tokenContractAddress) {
      throw new Error(`Token contract address is required for project ${project.projectId}`)
    }

    // Get actual decimals from indexer API
    const defaultDecimals = await this.getTokenDecimals(project.tokenContractAddress, project.blockchainToLaunchOn)

    const chainInfo: TokenChainInfo = {
      id: chainData.id,
      address: project.tokenContractAddress || '',
      decimals: defaultDecimals,
      isNative: false,
    }

    return [chainInfo]
  }

  private async getTokenDecimals(tokenAddress: string, blockchain: string): Promise<number> {
    const chainData = await this.chainsService.getChainById(blockchain)

    if (!chainData) {
      throw new Error(`Chain not found: ${blockchain}`)
    }

    const tokenDetails = await this.poolIndexerService.getTokenDetails(tokenAddress, chainData.chainId.toString())

    this.logger.log(`Got token details from indexer: ${JSON.stringify(tokenDetails)}`)

    const decimals = Number(tokenDetails.decimals)

    if (decimals === 0 || Number.isNaN(decimals)) {
      throw new Error(`Decimals not found for token ${tokenAddress} on chain ${chainData.chainId}`)
    }

    return decimals
  }

  private parseTokenAmount(amount: string | number): number {
    // Use BigNumber for safe parsing of large token amounts
    try {
      return new BigNumber(amount.toString()).toNumber()
    } catch {
      return 0
    }
  }

  private calculateFDV(project: ProjectData): number {
    // FDV = Total Supply * Token Price using BigNumber for precision
    try {
      const totalSupply = new BigNumber(project.totalTokenSupply.toString())
      const priceUsdc = new BigNumber(project.tgePriceUsdc.toString())

      return totalSupply.multipliedBy(priceUsdc).toNumber()
    } catch {
      return 0
    }
  }

  private calculateCirculatingSupply(project: ProjectData): number {
    // Calculate circulating supply based on allocation percentage using BigNumber
    try {
      const totalSupply = new BigNumber(project.totalTokenSupply.toString())
      const circulatingPercentage = new BigNumber(project.allocationCirculatingSupplyCaishen.toString())

      return totalSupply.multipliedBy(circulatingPercentage).dividedBy(100).toNumber()
    } catch {
      return 0
    }
  }
}
