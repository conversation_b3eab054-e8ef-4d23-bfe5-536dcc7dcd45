import { Controller, Get, Param, Query, NotFoundException } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiParam } from '@nestjs/swagger'

import { ProjectsService } from '../projects.service'
import { PublicProjectsService } from '../services/public-projects.service'

@ApiTags('Public Projects')
@Controller('public/projects')
export class PublicController {
  constructor(
    private readonly publicProjectsService: PublicProjectsService,
    private readonly projectsService: ProjectsService,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Get list of live projects with liquidity provided',
    description: `
    Returns a list of live projects with liquidity provided for public consumption.
    
    **Filters:**
    - Category filter: Filter projects by their current state relative to TGE date
      - **upcoming**: Projects not yet launched (today < TGE date)
      - **exclusive**: Projects in exclusive trading period (TGE date ≤ today < TGE date + exclusive period)
      - **live**: Projects in public trading (today ≥ TGE date + exclusive period)
    - Blockchain filter: Filter by blockchain network
    - Search: Search by project name or token symbol
    
    **Note:** Only projects with status = 'liquidity_provided' are returned (projects that are live and trading).
    `,
  })
  @ApiQuery({
    name: 'category',
    required: false,
    enum: ['upcoming', 'exclusive', 'live'],
    description:
      'Filter by project state: upcoming (not launched), exclusive (exclusive trading), live (public trading)',
  })
  @ApiQuery({ name: 'blockchain', required: false, description: 'Filter by blockchain network' })
  @ApiQuery({ name: 'search', required: false, description: 'Search by project name or token symbol' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 20, max: 100)' })
  @ApiQuery({
    name: 'isCurated',
    required: false,
    type: Boolean,
    description: 'Filter by curated projects (true/false)',
  })
  @ApiResponse({
    status: 200,
    description: 'Live projects with liquidity provided retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        projects: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              projectId: { type: 'string', example: 'a356fe0c-4cb7-4fe2-a45b-06c2faf02c36' },
              projectName: { type: 'string', example: 'DeFi Revolution' },
              tokenSymbol: { type: 'string', example: 'DEFI' },
              blockchainToLaunchOn: { type: 'string', example: 'ethereum' },
              startDate: { type: 'string', example: '2025-07-01' },
              endDate: { type: 'string', example: '2025-07-08' },
              status: { type: 'string', example: 'liquidity_provided' },
              initialPrice: { type: 'string', example: '0.001000000' },
              tokenThumbnailImageUrl: { type: 'string', example: 'https://example.com/logo.png' },
              headerDescription: { type: 'string', example: 'The future of DeFi' },
              bannerBackgroundImageUrl: { type: 'string', example: 'https://example.com/banner.png' },
              tokenId: { type: 'string', example: 'token-abc123', nullable: true, description: 'Associated token ID' },
              launchType: { type: 'string', enum: ['fair_launch', 'curated'], example: 'fair_launch' },
              isCurated: { type: 'boolean', example: false },
              partnerName: { type: 'string', example: 'Partner Name', nullable: true },
              categories: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    categoryId: { type: 'number', example: 1 },
                    categoryName: { type: 'string', example: 'DeFi' },
                  },
                },
              },
              socialLinks: {
                type: 'object',
                properties: {
                  twitter: { type: 'string', example: 'https://twitter.com/project' },
                  discord: { type: 'string', example: 'https://discord.gg/project' },
                  telegram: { type: 'string', example: 'https://t.me/project' },
                  website: { type: 'string', example: 'https://project.com' },
                },
              },
            },
          },
        },
        pagination: {
          type: 'object',
          properties: {
            page: { type: 'number', example: 1 },
            limit: { type: 'number', example: 20 },
            total: { type: 'number', example: 45 },
            hasMore: { type: 'boolean', example: true },
          },
        },
      },
    },
  })
  async getApprovedProjects(
    @Query('category') category?: 'upcoming' | 'exclusive' | 'live',
    @Query('blockchain') blockchain?: string,
    @Query('search') search?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('isCurated') isCurated?: boolean,
  ) {
    return this.publicProjectsService.getApprovedProjects({
      category,
      blockchain,
      search,
      page: page || 1,
      limit: Math.min(limit || 20, 100),
      isCurated,
    })
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get full project details by ID',
    description: `
    Returns complete details about a live project by its ID.
    
    **Restrictions:**
    - Only projects with liquidity provided can be accessed
    - Returns 404 if project doesn't have liquidity provided or doesn't exist
    
    **Returns all project information including:**
    - Basic details (name, description, website)
    - Token information (symbol, supply, price)
    - TGE details (dates, trading period)
    - Team and social links
    - Categories and audits
    - Rich text content (project details, tokenomics, etc.)
    `,
  })
  @ApiParam({
    name: 'id',
    description: 'Project ID',
    example: 'a356fe0c-4cb7-4fe2-a45b-06c2faf02c36',
  })
  @ApiResponse({
    status: 200,
    description: 'Project details retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        projectId: { type: 'string' },
        projectName: { type: 'string' },
        projectWebsite: { type: 'string' },
        blockchainToLaunchOn: { type: 'string' },
        startDate: { type: 'string', example: '2025-07-01T00:00:00.000Z', description: 'TGE launch start date' },
        endDate: {
          type: 'string',
          example: '2025-07-08T00:00:00.000Z',
          description: 'Exclusive trading period end date',
        },
        poolAddress: {
          type: 'string',
          example: '0x1234567890abcdef1234567890abcdef12345678',
          nullable: true,
          description: 'Pool address (null if not created yet)',
        },
        projectDetails: { type: 'object', description: 'Rich text content (Editor.js format)' },
        teamIntroduction: { type: 'object', description: 'Rich text content (Editor.js format)' },
        teamPublicProfileLinks: { type: 'string' },
        fundraiseHistory: { type: 'object', description: 'Rich text content (Editor.js format)' },
        totalRaisedUsd: { type: 'string' },
        socialLinks: {
          type: 'object',
          properties: {
            twitter: { type: 'string' },
            discord: { type: 'string' },
            telegram: { type: 'string' },
            farcaster: { type: 'string' },
            website: { type: 'string' },
          },
        },
        tokenomicsDetails: { type: 'object', description: 'Rich text content (Editor.js format)' },
        tokenInfo: {
          type: 'object',
          properties: {
            tokenName: { type: 'string' },
            tokenSymbol: { type: 'string' },
            tokenContractAddress: { type: 'string' },
            poolAddress: { type: 'string', nullable: true, description: 'Pool address (also available at top level)' },
            totalTokenSupply: { type: 'string' },
            tokenLaunchMarketCapUsd: { type: 'string' },
            tokenThumbnailImageUrl: { type: 'string' },
          },
        },
        tgeDetails: {
          type: 'object',
          properties: {
            tgeType: { type: 'string' },
            allocationTotalSupplyCaishen: { type: 'string' },
            allocationCirculatingSupplyCaishen: { type: 'string' },
            acceptedCurrenciesForPairing: { type: 'string' },
            tgePriceUsdc: { type: 'string' },
            exclusiveTradingPeriodHours: { type: 'number' },
            expectedTgeLaunchDate: { type: 'string' },
            liquidityActivationDate: { type: 'string' },
            minAmountPerTrade: { type: 'string' },
            // maxAmountPerTradeSol: removed - no longer needed
          },
        },
        categories: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              categoryId: { type: 'number' },
              categoryName: { type: 'string' },
            },
          },
        },
        audits: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              auditId: { type: 'string' },
              auditDate: { type: 'string' },
              auditingFirm: { type: 'string' },
              auditReportUrl: { type: 'string' },
              auditingFirmLogoUrl: { type: 'string' },
            },
          },
        },
        submissionStatus: { type: 'string', example: 'liquidity_provided' },
        tokenId: { type: 'string', example: 'token-abc123', nullable: true, description: 'Associated token ID' },
        launchType: { type: 'string', enum: ['fair_launch', 'curated'], example: 'fair_launch' },
        isCurated: { type: 'boolean', example: false },
        partnerName: { type: 'string', example: 'Partner Name', nullable: true },
        createdAt: { type: 'string' },
        updatedAt: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Project not found or liquidity not provided',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Project not found or liquidity not provided' },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  async getProjectById(@Param('id') projectId: string) {
    const project = await this.publicProjectsService.getApprovedProjectById(projectId)

    if (!project) {
      throw new NotFoundException('Project not found or liquidity not provided')
    }

    return project
  }
}
